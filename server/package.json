{"name": "todo-middleware-server", "version": "1.0.0", "description": "Node.js middleware server for TodoList application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["nodejs", "express", "middleware", "todolist"], "author": "", "license": "MIT"}