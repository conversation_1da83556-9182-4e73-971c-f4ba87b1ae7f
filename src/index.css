/* 全局基础样式 */
html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: #2c3e50;
}

/* 移除默认的焦点轮廓，使用自定义样式 */
*:focus {
  outline: none;
}

/* 确保所有交互元素都有合适的焦点指示 */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid #6c757d;
  outline-offset: 2px;
}