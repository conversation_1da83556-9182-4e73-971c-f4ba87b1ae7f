/* 日期选择器样式 */
.date-picker {
  position: relative;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.date-picker input[type="date"] {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 1rem;
  border: 1px solid #e7a6e3;
  border-radius: 12px;
  font-size: 1rem;
  background: #f9f4fb;
  z-index: 1000;
  margin-top: 0.5rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  font-family: -apple-system, system-ui, BlinkMacSystemFont;
  transition: all 0.3s ease;
  width: auto;
  min-width: 240px;
  color: #a259c6;
}

.date-picker input[type="date"]::-webkit-calendar-picker-indicator {
  background-color: transparent;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.date-picker input[type="date"]::-webkit-calendar-picker-indicator:hover {
  background-color: #f0f4f8;
}

.date-picker input[type="date"]:focus {
  outline: none;
  border-color: #a259c6;
  box-shadow: 0 8px 24px rgba(162, 89, 198, 0.15);
}

.date-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #e7a6e3 0%, #a259c6 100%);
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.3);
}

.date-button:hover {
  background: linear-gradient(135deg, #a259c6 0%, #c084d7 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* 全局样式重置和基础设置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #f3e7fa 0%, #f8c3e0 100%);
  color: #4b2956;
  line-height: 1.6;
  min-height: 100vh;
}

#root {
  max-width: 650px;
  margin: 0 auto;
  padding: 2.5rem 1.5rem;
  min-height: 100vh;
}

/* 标题样式 */
h1 {
  text-align: center;
  margin-bottom: 2.5rem;
  font-size: 3.0rem;
  font-weight: 600;
  color: #a259c6;
  letter-spacing: -1px;
  text-shadow: 0 4px 12px rgba(162, 89, 198, 0.08);
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2.5rem;
}
.login-form input {
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  min-height: 48px;
  max-height: 120px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(121, 67, 67, 0.4);
}
.login-form button {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #e7a6e3 0%, #f8c3e0 100%);
  color: #a259c6;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.3);
}
.login-form button:hover {
  background: linear-gradient(135deg, #a259c6 0%, #e7a6e3 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(125, 111, 108, 0.4);
}
/* 输入区域样式 */
.input-todo {
  display: flex;
  gap: 1rem;
  margin-bottom: 2.5rem;
  align-items: flex-start;
  background: rgba(250, 240, 255, 0.95);
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(162, 89, 198, 0.15);
}

.todo-input {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e3c6f7;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  min-height: 48px;
  max-height: 120px;
  background-color: #f9f4fb;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(162, 89, 198, 0.08);
  color: #6d3a7b;
}

.todo-input:focus {
  outline: none;
  border-color: #e7a6e3;
  box-shadow: 0 4px 16px rgba(162, 89, 198, 0.15);
  transform: translateY(-2px);
}

.todo-input::placeholder {
  color: #adb5bd;
}

.add-todo {
  padding: 1rem 2rem;
  margin-top: 10px;
  background: linear-gradient(135deg, #e7a6e3 0%, #f8c3e0 100%);
  color: #a259c6;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.3);
}

.add-todo:hover {
  background-color: #d6b4e7;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.add-todo:active {
  transform: translateY(0);
}

/* Todo列表样式 */
.todo-list ul {
  list-style: none;
  overflow: visible;
}

.todo-list {
  overflow: visible;
}

/* 待办事项列表容器动画 */
.todo-list-container {
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.todo-list-container.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.todo-list-container.fade-out {
  opacity: 0.3;
  transform: translateY(15px);
}

/* 为每个待办事项添加进入动画 */
.todo-list li {
  background: rgba(252, 244, 255, 0.98);
  border: 1px solid rgba(226, 180, 238, 0.4);
  border-radius: 16px;
  padding: 1.25rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(162, 89, 198, 0.08);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: visible;
  animation: slideInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 待办事项进入动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(25px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为不同索引的待办事项添加延迟动画 */
.todo-list li:nth-child(1) { animation-delay: 0.05s; }
.todo-list li:nth-child(2) { animation-delay: 0.1s; }
.todo-list li:nth-child(3) { animation-delay: 0.15s; }
.todo-list li:nth-child(4) { animation-delay: 0.2s; }
.todo-list li:nth-child(5) { animation-delay: 0.25s; }
.todo-list li:nth-child(6) { animation-delay: 0.3s; }
.todo-list li:nth-child(7) { animation-delay: 0.35s; }
.todo-list li:nth-child(8) { animation-delay: 0.4s; }
.todo-list li:nth-child(9) { animation-delay: 0.45s; }
.todo-list li:nth-child(10) { animation-delay: 0.5s; }

.todo-list li::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6c757d, #adb5bd);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.todo-list li:hover {
  box-shadow: 0 8px 32px rgba(162, 89, 198, 0.13);
  border-color: rgba(162, 89, 198, 0.25);
  transform: translateY(-4px);
}

.todo-list li:hover::before {
  opacity: 0;
}

/* 复选框样式 */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: #a259c6;
  border-radius: 4px;
}

/* Todo文本样式 */
.todo-list span {
  flex: 1;
  font-size: 1rem;
  word-break: break-word;
  transition: all 0.3s ease;
}

.todo-list span.done {
  text-decoration: line-through;
  color: #6c757d;
  opacity: 0.6;
}

/* 编辑输入框样式 */
.todo-list input[type="text"] {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #6c757d;
  border-radius: 10px;
  font-size: 1rem;
  font-family: inherit;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.todo-list input[type="text"]:focus {
  outline: none;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.15);
  transform: translateY(-1px);
}

/* 按钮样式 */
.todo-list button {
  padding: 0.6rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  color: #495057;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5px);
}

.todo-list button:hover {
  background: rgba(248, 249, 250, 0.95);
  border-color: #adb5bd;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.todo-list button:active {
  transform: translateY(0);
}

.todo-list li button:last-child {
  background-color: #d0a2e8;
  color: #572838;
  border-color: #d0a2e8;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.todo-list li button:last-child:hover {
  background-color: #d0a2e8;
  color: #4f242b;
  border-color: #d0a2e8;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* 保存和取消按钮样式 */
.todo-list li button:nth-last-child(2) {
  background-color: #d0a2e8;
  color: #4a2128;
  border-color: #d0a2e8;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.todo-list li button:nth-last-child(2):hover {
  background-color: #d0a2e8;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* 三点菜单样式 */
.menu-container {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 10;
}

/* 当菜单打开时，提升整个容器的z-index */
.menu-container.menu-open {
  z-index: 10000;
}

/* 确保打开菜单的todo项在最上层 */
.todo-list li.menu-active {
  z-index: 10000;
  position: relative;
}

.user-info button {
  padding: 0.5rem 1rem;
  border: 1px solid #414e5b;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.9);}

.user-info button:hover {
  background: rgba(248, 249, 250, 0.95);
  border-color: #adb5bd;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-button {
  background: none !important;
  border: none !important;
  font-size: 1.2rem !important;
  color: #6c757d !important;
  cursor: pointer;
  padding: 0.5rem !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease !important;
  box-shadow: none !important;
}

.menu-button:hover {
  background: rgba(108, 117, 125, 0.1) !important;
  transform: none !important;
}

.menu-button.active {
  background: rgba(108, 117, 125, 0.15) !important;
  color: #495057 !important;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(233, 236, 239, 0.5);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  z-index: 9999;
  min-width: 100px;
  overflow: visible;
  animation: fadeIn 0.2s ease;
  white-space: nowrap;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  display: block !important;
  width: 100% !important;
  padding: 0.75rem 1rem !important;
  background: none !important;
  border: none !important;
  text-align: left !important;
  color: #495057 !important;
  font-size: 0.875rem !important;
  cursor: pointer;
  transition: background-color 0.2s ease !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  position: relative;
  z-index: 10001;
}

.menu-item:hover {
  background: rgba(108, 117, 125, 0.1) !important;
  transform: none !important;
}

.menu-item:not(:last-child) {
  border-bottom: 1px solid rgba(233, 236, 239, 0.3);
}

/* 确保菜单在屏幕边缘时的显示 */
.dropdown-menu.align-left {
  right: auto;
  left: 0;
}

/* 为最后几个项目调整菜单位置 */
.todo-list li:nth-last-child(-n+2) .dropdown-menu {
  top: auto;
  bottom: calc(100% + 5px);
}

/* 响应式设计 */
@media (max-width: 640px) {
  #root {
    padding: 1.5rem 1rem;
  }

  h1 {
    font-size: 2.2rem;
    margin-bottom: 2rem;
  }

  .input-todo {
    flex-direction: column;
    gap: 1rem;
    padding: 1.25rem;
  }

  .todo-list li {
    flex-wrap: wrap;
    gap: 0.75rem;
    padding: 1rem;
  }

  .todo-list button {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }

  .menu-button {
    width: 36px !important;
    height: 36px !important;
    font-size: 1.4rem !important;
  }

  .dropdown-menu {
    min-width: 120px;
    right: -10px;
    top: calc(100% + 8px);
  }

  .menu-item {
    padding: 1rem 1.25rem !important;
    font-size: 0.9rem !important;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(233, 236, 239, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(162, 89, 198, 0.4);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(162, 89, 198, 0.7);
}